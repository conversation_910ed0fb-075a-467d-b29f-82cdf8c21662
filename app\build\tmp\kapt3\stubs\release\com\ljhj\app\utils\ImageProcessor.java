package com.ljhj.app.utils;

/**
 * 优化的图片处理工具 - 防止OOM和内存泄漏
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0002\b\t\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J:\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\b\u0010\u0012\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0018J2\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00150\u001a2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\b\u0010\u0012\u001a\u0004\u0018\u00010\u00132\b\b\u0002\u0010\u0017\u001a\u00020\u0015H\u0002J\b\u0010\u001b\u001a\u00020\u001cH\u0002J*\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u0011\u001a\u00020\u00102\b\b\u0002\u0010\u001f\u001a\u00020 2\b\b\u0002\u0010!\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\"J\u0012\u0010#\u001a\u0004\u0018\u00010\u00102\u0006\u0010$\u001a\u00020%H\u0002J\u0012\u0010&\u001a\u0004\u0018\u00010\u00102\u0006\u0010$\u001a\u00020%H\u0002J\"\u0010\'\u001a\u0004\u0018\u00010\u00102\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\u00062\u0006\u0010*\u001a\u00020+H\u0002J\"\u0010,\u001a\u0004\u0018\u00010\u00102\u0006\u0010-\u001a\u00020\u00102\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\u0006H\u0002J\b\u0010.\u001a\u00020\u0004H\u0002J\b\u0010/\u001a\u00020\u0006H\u0002J\u0010\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u0006H\u0002J\u0018\u00102\u001a\u0004\u0018\u00010\u00102\u0006\u0010$\u001a\u00020%H\u0086@\u00a2\u0006\u0002\u00103J\b\u00104\u001a\u00020\u001cH\u0002J\b\u00105\u001a\u00020\u001cH\u0002J\b\u00106\u001a\u000207H\u0002JP\u00108\u001a\u0004\u0018\u00010\u00102\u0006\u0010$\u001a\u00020%2\b\u0010\u0012\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\b\b\u0002\u0010\u000f\u001a\u00020\u001c2\b\b\u0002\u0010\u0017\u001a\u00020\u00152\b\b\u0002\u00109\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010:J\u0018\u0010;\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010<\u001a\u00020\u0006H\u0002J \u0010=\u001a\u00020\u00102\u0006\u0010>\u001a\u00020\u00102\b\b\u0002\u00109\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010?R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006@"}, d2 = {"Lcom/ljhj/app/utils/ImageProcessor;", "", "()V", "CRITICAL_MEMORY_THRESHOLD", "", "HIGH_QUALITY", "", "LOW_MEMORY_THRESHOLD", "LOW_QUALITY", "MAX_BITMAP_SIZE", "MEDIUM_QUALITY", "TARGET_HEIGHT", "WATERMARK_LINE_SPACING_RATIO", "WATERMARK_PADDING_RATIO", "WATERMARK_TEXT_SIZE_RATIO", "addWatermark", "Landroid/graphics/Bitmap;", "bitmap", "location", "Landroid/location/Location;", "companyName", "", "siteName", "remarkText", "(Landroid/graphics/Bitmap;Landroid/location/Location;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "buildWatermarkLines", "", "checkMemoryAvailable", "", "compressImage", "", "format", "Landroid/graphics/Bitmap$CompressFormat;", "quality", "(Landroid/graphics/Bitmap;Landroid/graphics/Bitmap$CompressFormat;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "convertJpegToBitmap", "imageProxy", "Landroidx/camera/core/ImageProxy;", "convertYuv420ToBitmap", "createBitmapSafely", "width", "height", "config", "Landroid/graphics/Bitmap$Config;", "createScaledBitmapSafely", "source", "getMemoryUsageRatio", "getOptimalQuality", "getOptimalTargetHeight", "originalHeight", "imageProxyToBitmap", "(Landroidx/camera/core/ImageProxy;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isCriticalMemory", "isLowMemory", "performMemoryCleanup", "", "processImage", "targetHeight", "(Landroidx/camera/core/ImageProxy;Landroid/location/Location;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "rotateImage", "rotationDegrees", "scaleImage", "originalBitmap", "(Landroid/graphics/Bitmap;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public final class ImageProcessor {
    private static final int TARGET_HEIGHT = 1920;
    private static final int MAX_BITMAP_SIZE = 31457280;
    private static final float WATERMARK_TEXT_SIZE_RATIO = 0.02F;
    private static final float WATERMARK_PADDING_RATIO = 0.02F;
    private static final float WATERMARK_LINE_SPACING_RATIO = 0.2F;
    private static final float LOW_MEMORY_THRESHOLD = 0.8F;
    private static final float CRITICAL_MEMORY_THRESHOLD = 0.9F;
    private static final int HIGH_QUALITY = 95;
    private static final int MEDIUM_QUALITY = 85;
    private static final int LOW_QUALITY = 70;
    @org.jetbrains.annotations.NotNull()
    public static final com.ljhj.app.utils.ImageProcessor INSTANCE = null;
    
    private ImageProcessor() {
        super();
    }
    
    private final float getMemoryUsageRatio() {
        return 0.0F;
    }
    
    private final boolean checkMemoryAvailable() {
        return false;
    }
    
    private final boolean isLowMemory() {
        return false;
    }
    
    private final boolean isCriticalMemory() {
        return false;
    }
    
    private final void performMemoryCleanup() {
    }
    
    private final android.graphics.Bitmap createBitmapSafely(int width, int height, android.graphics.Bitmap.Config config) {
        return null;
    }
    
    /**
     * 高效的ImageProxy转Bitmap - 防止OOM
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object imageProxyToBitmap(@org.jetbrains.annotations.NotNull()
    androidx.camera.core.ImageProxy imageProxy, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 转换YUV420格式
     */
    private final android.graphics.Bitmap convertYuv420ToBitmap(androidx.camera.core.ImageProxy imageProxy) {
        return null;
    }
    
    /**
     * 转换JPEG格式
     */
    private final android.graphics.Bitmap convertJpegToBitmap(androidx.camera.core.ImageProxy imageProxy) {
        return null;
    }
    
    /**
     * 智能图片质量选择
     */
    private final int getOptimalQuality() {
        return 0;
    }
    
    /**
     * 智能目标尺寸计算
     */
    private final int getOptimalTargetHeight(int originalHeight) {
        return 0;
    }
    
    /**
     * 高效的图片缩放
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object scaleImage(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap originalBitmap, int targetHeight, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 安全的图片缩放
     */
    private final android.graphics.Bitmap createScaledBitmapSafely(android.graphics.Bitmap source, int width, int height) {
        return null;
    }
    
    /**
     * 添加水印
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addWatermark(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.Nullable()
    android.location.Location location, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, @org.jetbrains.annotations.NotNull()
    java.lang.String siteName, @org.jetbrains.annotations.NotNull()
    java.lang.String remarkText, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 构建水印文本行
     */
    private final java.util.List<java.lang.String> buildWatermarkLines(java.lang.String companyName, java.lang.String siteName, android.location.Location location, java.lang.String remarkText) {
        return null;
    }
    
    /**
     * 压缩图片到指定质量
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object compressImage(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap.CompressFormat format, int quality, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super byte[]> $completion) {
        return null;
    }
    
    /**
     * 完整的图片处理流程
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object processImage(@org.jetbrains.annotations.NotNull()
    androidx.camera.core.ImageProxy imageProxy, @org.jetbrains.annotations.Nullable()
    android.location.Location location, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, @org.jetbrains.annotations.NotNull()
    java.lang.String siteName, boolean addWatermark, @org.jetbrains.annotations.NotNull()
    java.lang.String remarkText, int targetHeight, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 旋转图片
     */
    private final android.graphics.Bitmap rotateImage(android.graphics.Bitmap bitmap, int rotationDegrees) {
        return null;
    }
}