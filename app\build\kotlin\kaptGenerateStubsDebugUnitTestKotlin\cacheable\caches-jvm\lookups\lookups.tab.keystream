  ExampleUnitTest com.ljhj.app  Test com.ljhj.app.ExampleUnitTest  Assert 	org.junit  Test 	org.junit  Location android.location  Double com.ljhj.app  ImageProcessorRemarkTest com.ljhj.app  Double %com.ljhj.app.ImageProcessorRemarkTest  Location %com.ljhj.app.ImageProcessorRemarkTest  Test %com.ljhj.app.ImageProcessorRemarkTest  ImageProcessor com.ljhj.app.utils  Method java.lang.reflect  Double kotlin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      