package com.ljhj.app.utils

import android.graphics.*
import android.location.Location
import androidx.camera.core.ImageProxy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * 优化的图片处理工具 - 防止OOM和内存泄漏
 */
object ImageProcessor {

    // 性能优化配置
    private const val TARGET_HEIGHT = 1920
    private const val MAX_BITMAP_SIZE = 30 * 1024 * 1024 // 降低到30MB
    private const val WATERMARK_TEXT_SIZE_RATIO = 0.02f
    private const val WATERMARK_PADDING_RATIO = 0.02f
    private const val WATERMARK_LINE_SPACING_RATIO = 0.2f

    // 内存阈值配置
    private const val LOW_MEMORY_THRESHOLD = 0.8f // 80%内存使用率
    private const val CRITICAL_MEMORY_THRESHOLD = 0.9f // 90%内存使用率

    // 图片质量配置
    private const val HIGH_QUALITY = 95
    private const val MEDIUM_QUALITY = 85
    private const val LOW_QUALITY = 70

    // 增强的内存监控
    private fun getMemoryUsageRatio(): Float {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        return usedMemory.toFloat() / maxMemory.toFloat()
    }

    private fun checkMemoryAvailable(): Boolean {
        return getMemoryUsageRatio() < LOW_MEMORY_THRESHOLD
    }

    private fun isLowMemory(): Boolean {
        return getMemoryUsageRatio() > LOW_MEMORY_THRESHOLD
    }

    private fun isCriticalMemory(): Boolean {
        return getMemoryUsageRatio() > CRITICAL_MEMORY_THRESHOLD
    }

    // 智能内存清理
    private fun performMemoryCleanup() {
        System.gc()
        ResourceManager.clearBitmapCache()
        Thread.sleep(100) // 给GC时间
    }

    // 智能Bitmap创建
    private fun createBitmapSafely(width: Int, height: Int, config: Bitmap.Config): Bitmap? {
        return try {
            // 检查内存状态
            when {
                isCriticalMemory() -> {
                    Logger.w("Critical memory, cannot create bitmap: ${width}x${height}")
                    return null
                }
                isLowMemory() -> {
                    Logger.w("Low memory, performing cleanup before bitmap creation")
                    performMemoryCleanup()
                    if (!checkMemoryAvailable()) return null
                }
            }

            // 计算所需内存
            val requiredMemory = width * height * when(config) {
                Bitmap.Config.ARGB_8888 -> 4
                Bitmap.Config.RGB_565 -> 2
                else -> 4
            }

            if (requiredMemory > MAX_BITMAP_SIZE) {
                Logger.w("Bitmap too large: ${width}x${height}, required: ${requiredMemory / 1024 / 1024}MB")
                return null
            }

            Bitmap.createBitmap(width, height, config)
        } catch (e: OutOfMemoryError) {
            Logger.e("OOM creating bitmap: ${width}x${height}", e)
            performMemoryCleanup()
            null
        }
    }
    
    /**
     * 高效的ImageProxy转Bitmap - 防止OOM
     */
    suspend fun imageProxyToBitmap(imageProxy: ImageProxy): Bitmap? = withContext(Dispatchers.Default) {
        var bitmap: Bitmap? = null
        try {
            return@withContext ErrorHandler.safeExecute(
                operation = "图片转换",
                showToast = false
            ) {
                Logger.camera("Converting ImageProxy to Bitmap, format: ${imageProxy.format}")

                if (!checkMemoryAvailable()) {
                    Logger.w("Insufficient memory for image conversion")
                    return@safeExecute null
                }

                bitmap = when (imageProxy.format) {
                    ImageFormat.YUV_420_888 -> convertYuv420ToBitmap(imageProxy)
                    ImageFormat.JPEG -> convertJpegToBitmap(imageProxy)
                    else -> {
                        Logger.w("Unsupported image format: ${imageProxy.format}")
                        null
                    }
                }
                bitmap
            }
        } finally {
            // 确保ImageProxy被正确关闭
            try {
                imageProxy.close()
            } catch (e: Exception) {
                Logger.w("Error closing ImageProxy", e)
            }
        }
    }
    
    /**
     * 转换YUV420格式
     */
    private fun convertYuv420ToBitmap(imageProxy: ImageProxy): Bitmap? {
        val yBuffer = imageProxy.planes[0].buffer
        val uBuffer = imageProxy.planes[1].buffer
        val vBuffer = imageProxy.planes[2].buffer
        
        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()
        
        val nv21 = ByteArray(ySize + uSize + vSize)
        
        // 复制Y平面
        yBuffer.get(nv21, 0, ySize)
        
        // 交错复制UV平面
        val uPlane = imageProxy.planes[1]
        val vPlane = imageProxy.planes[2]
        
        val vRowStride = vPlane.rowStride
        val vPixelStride = vPlane.pixelStride
        val uRowStride = uPlane.rowStride
        val uPixelStride = uPlane.pixelStride
        
        var offset = ySize
        for (i in 0 until imageProxy.height / 2) {
            for (j in 0 until imageProxy.width / 2) {
                nv21[offset++] = vBuffer.get(i * vRowStride + j * vPixelStride)
                nv21[offset++] = uBuffer.get(i * uRowStride + j * uPixelStride)
            }
        }
        
        val yuvImage = YuvImage(nv21, ImageFormat.NV21, imageProxy.width, imageProxy.height, null)
        val out = ByteArrayOutputStream()
        yuvImage.compressToJpeg(Rect(0, 0, yuvImage.width, yuvImage.height), 90, out)
        val imageBytes = out.toByteArray()
        
        return BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
    }
    
    /**
     * 转换JPEG格式
     */
    private fun convertJpegToBitmap(imageProxy: ImageProxy): Bitmap? {
        val buffer = imageProxy.planes[0].buffer
        val bytes = ByteArray(buffer.remaining())
        buffer.get(bytes)
        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
    }
    
    /**
     * 智能图片质量选择
     */
    private fun getOptimalQuality(): Int {
        return when {
            isCriticalMemory() -> LOW_QUALITY
            isLowMemory() -> MEDIUM_QUALITY
            else -> HIGH_QUALITY
        }
    }

    /**
     * 智能目标尺寸计算
     */
    private fun getOptimalTargetHeight(originalHeight: Int): Int {
        return when {
            isCriticalMemory() && originalHeight > 1080 -> 1080
            isLowMemory() && originalHeight > 1440 -> 1440
            else -> minOf(originalHeight, TARGET_HEIGHT)
        }
    }

    /**
     * 高效的图片缩放
     */
    suspend fun scaleImage(
        originalBitmap: Bitmap,
        targetHeight: Int = TARGET_HEIGHT
    ): Bitmap = withContext(Dispatchers.Default) {
        val optimalHeight = getOptimalTargetHeight(targetHeight)
        Logger.camera("Scaling image from ${originalBitmap.width}x${originalBitmap.height} to target height $optimalHeight")

        val originalWidth = originalBitmap.width
        val originalHeight = originalBitmap.height

        if (originalHeight <= optimalHeight) {
            return@withContext originalBitmap
        }

        val targetWidth = (originalWidth * (optimalHeight.toFloat() / originalHeight)).toInt()

        // 根据内存状况选择缩放方式
        val scaledBitmap = if (isLowMemory()) {
            // 低内存时使用更节省内存的方式
            createScaledBitmapSafely(originalBitmap, targetWidth, optimalHeight)
        } else {
            // 正常情况使用高质量缩放
            Bitmap.createScaledBitmap(originalBitmap, targetWidth, optimalHeight, true)
        }

        // 释放原图内存
        if (scaledBitmap != null && scaledBitmap != originalBitmap) {
            originalBitmap.recycle()
        }

        return@withContext scaledBitmap ?: originalBitmap
    }

    /**
     * 安全的图片缩放
     */
    private fun createScaledBitmapSafely(source: Bitmap, width: Int, height: Int): Bitmap? {
        return try {
            Bitmap.createScaledBitmap(source, width, height, false) // 使用更快的缩放
        } catch (e: OutOfMemoryError) {
            Logger.e("OOM during scaling", e)
            performMemoryCleanup()
            null
        }
    }
    
    /**
     * 添加水印
     */
    suspend fun addWatermark(
        bitmap: Bitmap,
        location: Location?,
        companyName: String,
        siteName: String,
        remarkText: String = ""
    ): Bitmap = withContext(Dispatchers.Default) {
        Logger.camera("Adding watermark to image")
        
        val workingBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        val canvas = Canvas(workingBitmap)
        
        // 动态计算文本大小
        val baseTextSize = workingBitmap.height * WATERMARK_TEXT_SIZE_RATIO
        val paint = Paint().apply {
            color = Color.WHITE
            textSize = baseTextSize
            isAntiAlias = true
            setShadowLayer(2f, 1f, 1f, Color.BLACK)
        }
        
        // 准备水印文本
        val watermarkLines = buildWatermarkLines(companyName, siteName, location, remarkText)
        
        // 计算位置和间距
        val paddingX = workingBitmap.width * WATERMARK_PADDING_RATIO
        val paddingBottom = workingBitmap.height * WATERMARK_PADDING_RATIO
        val lineHeight = paint.descent() - paint.ascent()
        val lineSpacing = lineHeight * WATERMARK_LINE_SPACING_RATIO
        
        // 从底部向上绘制
        var currentY = workingBitmap.height - paddingBottom - paint.descent()
        
        for (i in watermarkLines.indices.reversed()) {
            val line = watermarkLines[i]
            canvas.drawText(line, paddingX, currentY, paint)
            currentY -= (lineHeight + lineSpacing)
        }
        
        return@withContext workingBitmap
    }
    
    /**
     * 构建水印文本行
     */
    private fun buildWatermarkLines(
        companyName: String,
        siteName: String,
        location: Location?,
        remarkText: String = ""
    ): List<String> {
        val lines = mutableListOf<String>()

        if (companyName.isNotBlank()) {
            lines.add("企业名: $companyName")
        }

        if (siteName.isNotBlank()) {
            lines.add("站点: $siteName")
        }

        location?.let {
            lines.add("GPS经纬度: %.4f, %.4f".format(it.latitude, it.longitude))
        }

        // 添加备注行（仅当有备注时）
        if (remarkText.isNotBlank()) {
            lines.add("备注: $remarkText")
        }

        val timeStamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        lines.add(timeStamp)

        return lines.filter { it.isNotBlank() }
    }
    
    /**
     * 压缩图片到指定质量
     */
    suspend fun compressImage(
        bitmap: Bitmap,
        format: Bitmap.CompressFormat = Bitmap.CompressFormat.WEBP,
        quality: Int = 80
    ): ByteArray = withContext(Dispatchers.Default) {
        Logger.camera("Compressing image with quality $quality")
        
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(format, quality, outputStream)
        return@withContext outputStream.toByteArray()
    }
    
    /**
     * 完整的图片处理流程
     */
    suspend fun processImage(
        imageProxy: ImageProxy,
        location: Location?,
        companyName: String,
        siteName: String,
        addWatermark: Boolean = true,
        remarkText: String = "",
        targetHeight: Int = TARGET_HEIGHT
    ): Bitmap? = withContext(Dispatchers.Default) {
        return@withContext ErrorHandler.safeExecute(
            operation = "图片处理",
            showToast = false
        ) {
            Logger.camera("Starting image processing pipeline")
            
            // 1. 转换为Bitmap
            val originalBitmap = imageProxyToBitmap(imageProxy) ?: return@safeExecute null
            
            // 2. 处理旋转
            val rotatedBitmap = rotateImage(originalBitmap, imageProxy.imageInfo.rotationDegrees)
            
            // 3. 缩放
            val scaledBitmap = scaleImage(rotatedBitmap, targetHeight)
            
            // 4. 添加水印（如果需要）
            val finalBitmap = if (addWatermark) {
                addWatermark(scaledBitmap, location, companyName, siteName, remarkText)
            } else {
                scaledBitmap
            }
            
            Logger.camera("Image processing completed")
            finalBitmap
        }
    }
    
    /**
     * 旋转图片
     */
    private fun rotateImage(bitmap: Bitmap, rotationDegrees: Int): Bitmap {
        if (rotationDegrees == 0) return bitmap
        
        val matrix = Matrix().apply { 
            postRotate(rotationDegrees.toFloat()) 
        }
        
        val rotatedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )
        
        if (rotatedBitmap != bitmap) {
            bitmap.recycle()
        }
        
        return rotatedBitmap
    }
}
