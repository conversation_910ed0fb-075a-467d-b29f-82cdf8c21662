package com.ljhj.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0098\u0001\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a\u0018\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007H\u0007\u001a(\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u0007H\u0007\u001a\u0018\u0010\u001d\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u001e\u001a\u00020\u001fH\u0007\u001a\u0010\u0010 \u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0016H\u0007\u001a\u0018\u0010!\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\"\u001a\u00020\u0007H\u0007\u001a\u0016\u0010#\u001a\u00020\u00142\u0006\u0010$\u001a\u00020\u000e2\u0006\u0010%\u001a\u00020&\u001a(\u0010\'\u001a\u0004\u0018\u00010(2\u0006\u0010$\u001a\u00020\u000e2\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020\u0007H\u0082@\u00a2\u0006\u0002\u0010,\u001a&\u0010-\u001a\u00020\u00142\u0006\u0010$\u001a\u00020\u000e2\u0006\u0010.\u001a\u00020(2\u0006\u0010+\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u0007\u001a@\u0010/\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u00100\u001a\u0002012\u0006\u00102\u001a\u0002032\u0006\u00104\u001a\u0002052\u0006\u00106\u001a\u0002072\u0006\u0010$\u001a\u00020\u000e2\u0006\u0010\"\u001a\u00020\u0007H\u0007\u001a \u00108\u001a\u00020\u00142\u0006\u00104\u001a\u0002052\u0006\u00106\u001a\u0002072\u0006\u0010$\u001a\u00020\u000eH\u0003\u001a~\u00109\u001a\u00020\u00142\u0006\u0010$\u001a\u00020\u000e2\b\u0010:\u001a\u0004\u0018\u00010;2\u0006\u0010<\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00022\b\u0010\u0017\u001a\u0004\u0018\u00010?2\u0006\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u00072\u0006\u0010@\u001a\u00020\u00072\u0006\u0010A\u001a\u00020\u00072\u0006\u0010B\u001a\u00020C2\u0006\u0010D\u001a\u00020\u001f2\u0006\u0010E\u001a\u00020\u001f2\u0012\u0010F\u001a\u000e\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u00140G\"\u0017\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\"\u001d\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0004\"\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00070\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0004\"%\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f*\u00020\u000e8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0011\u0010\u0012\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006H"}, d2 = {"ADD_WATERMARK_KEY", "Landroidx/datastore/preferences/core/Preferences$Key;", "", "getADD_WATERMARK_KEY", "()Landroidx/datastore/preferences/core/Preferences$Key;", "IMAGE_URIS_KEY", "", "", "getIMAGE_URIS_KEY", "TOKEN_KEY", "getTOKEN_KEY", "dataStore", "Landroidx/datastore/core/DataStore;", "Landroidx/datastore/preferences/core/Preferences;", "Landroid/content/Context;", "getDataStore", "(Landroid/content/Context;)Landroidx/datastore/core/DataStore;", "dataStore$delegate", "Lkotlin/properties/ReadOnlyProperty;", "AddSiteScreen", "", "navController", "Landroidx/navigation/NavController;", "currentLocation", "CameraScreen", "fusedLocationClient", "Lcom/google/android/gms/location/FusedLocationProviderClient;", "companyName", "siteName", "FullScreenImageScreen", "initialPage", "", "GalleryScreen", "MainScreen", "token", "retryUpload", "context", "task", "Lcom/ljhj/app/data/model/UploadTask;", "saveImageToGallery", "Landroid/net/Uri;", "bitmap", "Landroid/graphics/Bitmap;", "fileName", "(Landroid/content/Context;Landroid/graphics/Bitmap;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "scheduleUpload", "imageUri", "startLocationUpdates", "locationRequest", "Lcom/google/android/gms/location/LocationRequest;", "gmsLocationCallback", "Lcom/google/android/gms/location/LocationCallback;", "locationManager", "Landroid/location/LocationManager;", "locationListener", "Landroid/location/LocationListener;", "startSystemLocationUpdates", "takePhoto", "imageCapture", "Landroidx/camera/core/ImageCapture;", "executor", "Ljava/util/concurrent/Executor;", "addWatermark", "Landroid/location/Location;", "selectedType", "remarkText", "soundPool", "Landroid/media/SoundPool;", "shutterSoundId", "errorSoundId", "onImageCaptured", "Lkotlin/Function1;", "app_debug"})
public final class MainActivityKt {
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.properties.ReadOnlyProperty dataStore$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.util.Set<java.lang.String>> IMAGE_URIS_KEY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> ADD_WATERMARK_KEY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> TOKEN_KEY = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> getDataStore(@org.jetbrains.annotations.NotNull()
    android.content.Context $this$dataStore) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.datastore.preferences.core.Preferences.Key<java.util.Set<java.lang.String>> getIMAGE_URIS_KEY() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getADD_WATERMARK_KEY() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> getTOKEN_KEY() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MainScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String token) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void CameraScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.google.android.gms.location.FusedLocationProviderClient fusedLocationClient, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, @org.jetbrains.annotations.NotNull()
    java.lang.String siteName) {
    }
    
    @android.annotation.SuppressLint(value = {"MissingPermission"})
    public static final void startLocationUpdates(@org.jetbrains.annotations.NotNull()
    com.google.android.gms.location.FusedLocationProviderClient fusedLocationClient, @org.jetbrains.annotations.NotNull()
    com.google.android.gms.location.LocationRequest locationRequest, @org.jetbrains.annotations.NotNull()
    com.google.android.gms.location.LocationCallback gmsLocationCallback, @org.jetbrains.annotations.NotNull()
    android.location.LocationManager locationManager, @org.jetbrains.annotations.NotNull()
    android.location.LocationListener locationListener, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String token) {
    }
    
    @android.annotation.SuppressLint(value = {"MissingPermission"})
    private static final void startSystemLocationUpdates(android.location.LocationManager locationManager, android.location.LocationListener locationListener, android.content.Context context) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void GalleryScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    public static final void retryUpload(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.ljhj.app.data.model.UploadTask task) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void FullScreenImageScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, int initialPage) {
    }
    
    public static final void takePhoto(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    androidx.camera.core.ImageCapture imageCapture, @org.jetbrains.annotations.NotNull()
    java.util.concurrent.Executor executor, boolean addWatermark, @org.jetbrains.annotations.Nullable()
    android.location.Location currentLocation, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, @org.jetbrains.annotations.NotNull()
    java.lang.String siteName, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedType, @org.jetbrains.annotations.NotNull()
    java.lang.String remarkText, @org.jetbrains.annotations.NotNull()
    android.media.SoundPool soundPool, int shutterSoundId, int errorSoundId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.net.Uri, kotlin.Unit> onImageCaptured) {
    }
    
    /**
     * 保存图片到相册
     */
    private static final java.lang.Object saveImageToGallery(android.content.Context context, android.graphics.Bitmap bitmap, java.lang.String fileName, kotlin.coroutines.Continuation<? super android.net.Uri> $completion) {
        return null;
    }
    
    public static final void scheduleUpload(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.net.Uri imageUri, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddSiteScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String currentLocation) {
    }
}