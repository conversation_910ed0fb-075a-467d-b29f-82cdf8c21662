package com.ljhj.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u0014J\b\u0010\u001c\u001a\u00020\u0019H\u0014R\u001a\u0010\u0003\u001a\u00020\u0004X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000f\u001a\u00020\u0004X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0006\"\u0004\b\u0011\u0010\bR\u001a\u0010\u0012\u001a\u00020\u0013X\u0080.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017\u00a8\u0006\u001d"}, d2 = {"Lcom/ljhj/app/MainActivity;", "Landroidx/activity/ComponentActivity;", "()V", "errorSoundId", "", "getErrorSoundId$app_release", "()I", "setErrorSoundId$app_release", "(I)V", "fusedLocationClient", "Lcom/google/android/gms/location/FusedLocationProviderClient;", "requestPermissionsLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "", "shutterSoundId", "getShutterSoundId$app_release", "setShutterSoundId$app_release", "soundPool", "Landroid/media/SoundPool;", "getSoundPool$app_release", "()Landroid/media/SoundPool;", "setSoundPool$app_release", "(Landroid/media/SoundPool;)V", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "app_release"})
public final class MainActivity extends androidx.activity.ComponentActivity {
    private com.google.android.gms.location.FusedLocationProviderClient fusedLocationClient;
    public android.media.SoundPool soundPool;
    private int shutterSoundId = 0;
    private int errorSoundId = 0;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> requestPermissionsLauncher = null;
    
    public MainActivity() {
        super(0);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.media.SoundPool getSoundPool$app_release() {
        return null;
    }
    
    public final void setSoundPool$app_release(@org.jetbrains.annotations.NotNull()
    android.media.SoundPool p0) {
    }
    
    public final int getShutterSoundId$app_release() {
        return 0;
    }
    
    public final void setShutterSoundId$app_release(int p0) {
    }
    
    public final int getErrorSoundId$app_release() {
        return 0;
    }
    
    public final void setErrorSoundId$app_release(int p0) {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
}