<lint-module
    format="1"
    dir="D:\CODE\LJHJ\app"
    name=":app"
    type="APP"
    maven="绿佳环境:app:unspecified"
    agpVersion="8.11.1"
    buildFolder="build"
    bootClassPath="D:\Program Files\Android\SDK\platforms\android-36\android.jar;D:\Program Files\Android\SDK\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-36"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
